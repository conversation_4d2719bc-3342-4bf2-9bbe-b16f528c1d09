# QuantTrade

A comprehensive framework for quantitative trading and backtesting cryptocurrency strategies.

## Features

-   Backtest trading strategies on 1-minute, 4-hour, and daily timeframes
-   Calculate performance metrics: PnL, equity curve, Sharpe ratio, max drawdown
-   Visualize results with charts and summary statistics
-   Compare strategy performance across different timeframes
-   Includes several pre-built rule-based strategies

## Quick Start Guide

### 1. Setup Environment

#### Using uv (recommended)

```bash
# Install uv if you don't have it
pip install uv

# Create and activate a virtual environment
uv venv .venv
# On Windows:
.venv\Scripts\activate
# On macOS/Linux:
source .venv/bin/activate

# Install the package in development mode
uv pip install -e .
```

#### Using pip

```bash
# Create and activate a virtual environment
python -m venv .venv
# On Windows:
.venv\Scripts\activate
# On macOS/Linux:
source .venv/bin/activate

# Install the package in development mode
pip install -e .
```

### 2. Running Scripts

There are three main ways to run the scripts:

#### Option 1: Using the installed command-line scripts

After installing the package, you can use these commands from anywhere:

```bash
# Run a single backtest
backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy ma_crossover --timeframe 1d

# Run backtests on multiple timeframes
multi-backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy rsi

# Analyze data
analyze-data --file quanttrade/data/datasets/btcusd_1_min_data.csv

# Download cryptocurrency data
download-crypto --exchange binance --symbol BTC/USDT --timeframe 1d --start_date 2020-01-01

# Run CTREND strategy backtest
ctrend-backtest --data-file quanttrade/data/datasets/btcusdt_1d_data.csv --rolling-window 52 --lookback-period 1
```

#### Option 2: Using Python module syntax

```bash
# Run a single backtest
python -m quanttrade.scripts.run_backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy ma_crossover --timeframe 1d

# Run backtests on multiple timeframes
python -m quanttrade.scripts.run_multi_timeframe_backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy rsi

# Analyze data
python -m quanttrade.scripts.analyze_data --file quanttrade/data/datasets/btcusd_1_min_data.csv
```

#### Option 3: Running the script files directly

```bash
# Run a single backtest
python quanttrade/scripts/run_backtest.py --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy ma_crossover --timeframe 1d

# Run backtests on multiple timeframes
python quanttrade/scripts/run_multi_timeframe_backtest.py --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy rsi

# Analyze data
python quanttrade/scripts/analyze_data.py --file quanttrade/data/datasets/btcusd_1_min_data.csv
```

## Available Strategies

1. **Moving Average Crossover (`ma_crossover`)**

    - Description: Generates buy signals when a fast moving average crosses above a slow moving average, and sell signals when it crosses below.
    - Parameters:
        - `--fast-period`: Period for the fast moving average (default: 20)
        - `--slow-period`: Period for the slow moving average (default: 50)
    - Example:
        ```bash
        backtest --strategy ma_crossover --fast-period 10 --slow-period 30 --timeframe 4h
        ```

2. **RSI Mean Reversion (`rsi`)**

    - Description: Generates buy signals when RSI crosses above the oversold threshold, and sell signals when it crosses below the overbought threshold.
    - Parameters:
        - `--rsi-period`: Period for RSI calculation (default: 14)
        - `--rsi-overbought`: Overbought threshold (default: 70)
        - `--rsi-oversold`: Oversold threshold (default: 30)
    - Example:
        ```bash
        backtest --strategy rsi --rsi-period 14 --rsi-overbought 75 --rsi-oversold 25 --timeframe 1d
        ```

3. **MACD Trend Following (`macd`)**

    - Description: Generates buy signals when MACD crosses above the signal line, and sell signals when it crosses below.
    - No additional parameters
    - Example:
        ```bash
        backtest --strategy macd --timeframe 4h
        ```

4. **Bollinger Bands Mean Reversion (`bollinger`)**

    - Description: Generates buy signals when price crosses above the lower band, and sell signals when it crosses below the upper band.
    - Parameters:
        - `--bb-period`: Period for Bollinger Bands calculation (default: 20)
        - `--bb-std-dev`: Number of standard deviations for the bands (default: 2.0)
    - Example:
        ```bash
        backtest --strategy bollinger --bb-period 20 --bb-std-dev 2.5 --timeframe 1d
        ```

5. **MACD Price Divergence (`macd_divergence`)**

    - Description: Identifies bullish and bearish divergences between price and MACD.
    - Parameters:
        - `--peak-window`: Window size for peak/trough detection (default: 5)
    - Example:
        ```bash
        backtest --strategy macd_divergence --peak-window 5 --timeframe 1d
        ```

6. **Trend, Demand, and Supply (`trend_demand_supply`)**

    - Description: Identifies trends and trades at demand/supply zones.
    - Parameters:
        - `--lookback-period`: Period for trend identification (default: 20)
        - `--consolidation-threshold`: Threshold for identifying consolidation (default: 0.03)
    - Example:
        ```bash
        backtest --strategy trend_demand_supply --lookback-period 20 --timeframe 4h
        ```

7. **CTREND Factor (`ctrend`)**

    - Description: Implements the CTREND factor strategy using cross-sectional combined elastic net (CS-C-ENet).
    - Parameters:
        - `--rolling-window`: Number of periods for rolling estimation (default: 52)
        - `--lookback-period`: Number of periods to look back for signal generation (default: 1)
    - Example:
        ```bash
        ctrend-backtest --data-file quanttrade/data/datasets/btcusdt_1d_data.csv --rolling-window 52 --lookback-period 1
        ```

8. **Custom Strategy (`custom`)**
    - Description: A template for creating your own custom strategy.
    - Example:
        ```bash
        backtest --strategy custom --timeframe 1d
        ```

## Common Command-Line Options

All backtest scripts support these common options:

```
--data-file PATH       Path to the CSV file containing price data
--timeframe {1m,4h,1d} Timeframe for backtesting
--start-date DATE      Start date for backtesting (YYYY-MM-DD)
--end-date DATE        End date for backtesting (YYYY-MM-DD)
--initial-capital AMOUNT Initial capital for backtesting
--commission RATE      Commission rate for trades (as a decimal)
```

## Example Workflows

### Basic Workflow

1. Analyze your data to understand its characteristics:

    ```bash
    analyze-data --file quanttrade/data/datasets/btcusd_1_min_data.csv
    ```

2. Run a backtest with a specific strategy:

    ```bash
    backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy ma_crossover --timeframe 4h --start-date 2020-01-01 --end-date 2023-01-01
    ```

3. Compare performance across different timeframes:
    ```bash
    multi-backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy ma_crossover --start-date 2020-01-01 --end-date 2023-01-01
    ```

### Strategy Optimization Workflow

1. Test a strategy with different parameters:

    ```bash
    # Test with different fast/slow periods
    backtest --strategy ma_crossover --fast-period 10 --slow-period 30 --timeframe 4h
    backtest --strategy ma_crossover --fast-period 5 --slow-period 20 --timeframe 4h
    backtest --strategy ma_crossover --fast-period 20 --slow-period 50 --timeframe 4h
    ```

2. Compare the best parameters across timeframes:
    ```bash
    multi-backtest --strategy ma_crossover --fast-period 10 --slow-period 30
    ```

## Project Structure

```
quanttrade/
├── backtest/         # Backtesting framework
│   └── framework.py  # Core backtesting engine
├── data/             # Data loading and processing utilities
│   ├── datasets/     # Data files
│   └── loader.py     # Data loading functions
├── strategies/       # Trading strategies
│   ├── basic.py      # Basic trading strategies
│   ├── basic_pandas.py # Pandas-based implementations of basic strategies
│   ├── custom.py     # Custom trading strategies
│   └── trend_demand_supply_strategy.py # Trend, demand, and supply strategy
├── utils/            # Utility functions
│   ├── trade.py      # Trading utilities
│   └── trend_identifier.py # Trend identification utilities
├── scripts/          # Command-line scripts
│   ├── analyze_data.py               # Data analysis script
│   ├── download_crypto_data.py       # Cryptocurrency data download script
│   ├── identify_trend.py             # Trend identification script
│   ├── run_backtest.py               # Single backtest script
│   ├── run_ctrend_backtest.py        # CTREND strategy backtest script
│   ├── run_multi_timeframe_backtest.py  # Multi-timeframe backtest script
│   └── visualize_trend_chart.py      # Trend visualization script
└── notebooks/        # Jupyter notebooks for analysis
    ├── Algorithmic_Trading_Machine_Learning_Quant_Strategies.ipynb
    ├── CTREND_Strategy.ipynb         # CTREND strategy implementation
    └── Kmean.ipynb                   # K-means clustering analysis
```

## Creating Your Own Strategy

To create your own strategy, modify the `custom_strategy` function in `quanttrade/strategies/custom.py`:

```python
def custom_strategy(df: pd.DataFrame) -> pd.DataFrame:
    # Add technical indicators
    df = add_technical_indicators(df)

    # Initialize signal column
    df['signal'] = 0

    # Generate signals based on your rules
    for i in range(1, len(df)):
        # Example: Buy when RSI is oversold AND price is above 200 SMA
        if (df['RSI14'].iloc[i-1] < 30 and df['RSI14'].iloc[i] > 30 and
            df['Close'].iloc[i] > df['SMA200'].iloc[i]):
            df.loc[df.index[i], 'signal'] = 1  # Buy signal

        # Example: Sell when RSI is overbought OR price crosses below 200 SMA
        elif ((df['RSI14'].iloc[i-1] > 70 and df['RSI14'].iloc[i] < 70) or
              (df['Close'].iloc[i-1] > df['SMA200'].iloc[i-1] and df['Close'].iloc[i] < df['SMA200'].iloc[i])):
            df.loc[df.index[i], 'signal'] = -1  # Sell signal

    return df
```

Then run your custom strategy:

```bash
backtest --strategy custom --timeframe 4h
```

## Understanding Backtest Results

After running a backtest, results are saved in the `backtest_results` directory:

-   **Equity Curve**: Shows how your capital changes over time
-   **Drawdown Chart**: Shows the percentage decline from peak equity
-   **Trade PnL Chart**: Shows the profit/loss for each trade
-   **Performance Summary**: Text file with key metrics like Sharpe ratio, total PnL, etc.

For multi-timeframe backtests, a comparison of results across timeframes is also generated.

## Troubleshooting

-   **ImportError**: Make sure you've activated your virtual environment and installed the package with `uv pip install -e .` or `pip install -e .`
-   **FileNotFoundError**: Check that you're using the correct path to the data file
-   **No trades generated**: Ensure your strategy parameters are appropriate for the timeframe you're testing

## Working with Large Data Files

The repository is configured to exclude large data files from version control. To work with the project:

1. **Download data files separately**: Large data files like `btcusd_1_min_data.csv` should be downloaded separately and placed in the `quanttrade/data/datasets/` directory.

2. **Data sources**: You can obtain cryptocurrency data using the included download script:

    ```bash
    # Download daily BTC/USDT data from Binance
    download-crypto --exchange binance --symbol BTC/USDT --timeframe 1d --start_date 2020-01-01
    ```

    Or from these external sources:

    - [CryptoDataDownload](https://www.cryptodatadownload.com/data/)
    - [Kaggle Cryptocurrency Datasets](https://www.kaggle.com/datasets?search=cryptocurrency)
    - [Binance Historical Data](https://data.binance.vision/)

3. **Expected data format**: The scripts expect CSV files with at least these columns:

    - `timestamp` or `date`: Date and time of the candle
    - `open`: Opening price
    - `high`: Highest price in the period
    - `low`: Lowest price in the period
    - `close`: Closing price
    - `volume`: Trading volume

4. **Data preprocessing**: If your data is in a different format, you may need to preprocess it before using with the backtesting scripts.

## License

MIT
