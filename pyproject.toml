[project]
name = "quanttrade"
version = "0.1.0"
description = "Quantitative Trading and Backtesting Framework"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "binance-connector>=3.12.0",
    "binance-futures-connector>=4.1.0",
    "ccxt>=4.2.15",
    "matplotlib>=3.10.1",
    "numpy>=2.2.5",
    "pandas>=2.2.3",
    "pandas-datareader>=0.10.0",
    "pandas-ta>=0.3.14b0",
    "plotly>=5.22.0",
    "pyportfolioopt>=1.5.6",
    "python-dotenv>=1.1.0",
    "scikit-learn>=1.6.1",
    "seaborn>=0.13.2",
    "statsmodels>=0.14.4",
    "yfinance>=0.2.58",
]

[project.scripts]
backtest = "quanttrade.scripts.run_backtest:main"
multi-backtest = "quanttrade.scripts.run_multi_timeframe_backtest:main"
analyze-data = "quanttrade.scripts.analyze_data:main"
download-crypto = "quanttrade.scripts.download_crypto_data:main"
ctrend-backtest = "quanttrade.scripts.run_ctrend_backtest:main"

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"
