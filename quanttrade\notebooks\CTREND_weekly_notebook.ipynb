!pip install -q ccxt pandas pandas_ta statsmodels scikit-learn linearmodels tqdm binance-connector yfinance requests pycoingecko

import ccxt
import pandas as pd

def fetch_binance_spot_usdt_active_coins(min_volume=1, top_n=None):
    """
    Fetch Binance **spot** USDT‐quoted coins with 24 h quoteVolume > min_volume,
    dedupe so that each base asset appears only once (keeping its highest‐volume pair),
    and optionally return only the top_n bases.
    """
    # 1) Connect to Binance in spot mode
    exchange = ccxt.binance({
        'enableRateLimit': True,
        'options': { 'defaultType': 'spot' },
    })
    exchange.load_markets()

    # 2) Fetch all 24 h ticker stats (spot)
    print("Fetching 24 h spot tickers from Binance…")
    tickers = exchange.fetch_tickers()  # now only spot tickers, since defaultType='spot'

    # 3) Build a DataFrame of symbol vs. 24 h quoteVolume
    df = pd.DataFrame.from_dict(tickers, orient='index')[['symbol', 'quoteVolume']].copy()
    df['quoteVolume'] = pd.to_numeric(df['quoteVolume'], errors='coerce').fillna(0.0)

    # 4) Keep only USDT-quoted spot pairs
    df_usdt = df[df['symbol'].str.endswith('/USDT')].copy()

    # 5) Filter out zero—or very low—volume pairs
    df_usdt = df_usdt[df_usdt['quoteVolume'] > min_volume]

    # 6) Extract the base asset code (everything before the “/”)
    df_usdt['base'] = df_usdt['symbol'].str.split('/').str[0]

    # 7) Sort by descending 24 h USD volume
    df_usdt = df_usdt.sort_values('quoteVolume', ascending=False)

    # 8) Drop duplicate bases, keeping only the highest-volume pair per base
    df_unique = df_usdt.drop_duplicates(subset='base', keep='first')

    # 9) (Optional) If you only want the top N coins by volume, take head(top_n)
    if top_n is not None:
        df_unique = df_unique.head(top_n)

    df_unique = df_unique.reset_index(drop=True)
    print(f"→ Found {len(df_unique)} spot USDT-quoted coins with 24 h volume > {min_volume}")

    # Show the first 20 for verification
    print(df_unique[['base', 'symbol', 'quoteVolume']].head(20).to_string(index=False))
    return df_unique

if __name__ == "__main__":
    # Example usage:
    #   - Only coins with > $1,000 24 h USDT volume
    #   - Return up to top 600 bases
    df_spot = fetch_binance_spot_usdt_active_coins(min_volume=1_000, top_n=600)
    df_spot.to_csv("binance_spot_usdt_active.csv", index=False)


df_futures

import ccxt
import pandas as pd
from datetime import datetime, timedelta
from tqdm import tqdm
import time

def fetch_binance_spot_usdt_historical_weekly(symbols,
                                             timeframe='1w',
                                             history_weeks=104,
                                             min_data_length=None,
                                             rate_limit_delay=0.25):
    """
    (Same as before) Returns a dict { symbol: DataFrame } for each symbol
    that has ≥ min_data_length weekly candles since ~history_weeks ago.
    """
    exchange = ccxt.binance({
        'enableRateLimit': True,
        'options': {'defaultType': 'spot'}
    })
    exchange.load_markets()

    now = datetime.utcnow()
    since_dt = now - timedelta(weeks=history_weeks)
    since_ts = int(since_dt.timestamp() * 1000)

    if min_data_length is None:
        min_data_length = history_weeks

    results = {}
    for sym in tqdm(symbols, desc="Symbols processed", unit="symbol"):
        try:
            ohlcv = exchange.fetch_ohlcv(symbol=sym, timeframe=timeframe, since=since_ts)
            if len(ohlcv) < min_data_length:
                continue
            first_ts = ohlcv[0][0]
            one_week_ms = 7 * 24 * 3600 * 1000
            if first_ts > since_ts + one_week_ms:
                continue
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            results[sym] = df

        except ccxt.NetworkError:
            time.sleep(rate_limit_delay)
            try:
                ohlcv = exchange.fetch_ohlcv(symbol=sym, timeframe=timeframe, since=since_ts)
                if len(ohlcv) < min_data_length:
                    continue
                first_ts = ohlcv[0][0]
                one_week_ms = 7 * 24 * 3600 * 1000
                if first_ts > since_ts + one_week_ms:
                    continue
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
                results[sym] = df
            except Exception:
                continue

        except ccxt.ExchangeError:
            continue
        except Exception:
            continue

        time.sleep(rate_limit_delay)

    return results


if __name__ == "__main__":
    # 1) Load your list of USDT-quoted spot symbols from CSV
    df_spot = pd.read_csv("binance_spot_usdt_active.csv")
    symbols = df_spot['symbol'].tolist()

    # 2) Fetch two years of weekly data (104 candles)
    data_dict = fetch_binance_spot_usdt_historical_weekly(
        symbols=symbols,
        timeframe='1w',
        history_weeks=104,
        min_data_length=100,    # allow up to ~4 missing weeks
        rate_limit_delay=0.25
    )

    # 3) Save each symbol’s weekly history to CSV
    for sym, df in data_dict.items():
        fname = sym.replace("/", "_") + "_2y_weekly.csv"
        df.to_csv(fname, index=False)

    # 4) **Save the symbol list itself** (those that had ≥104 weeks)
    symbols_with_2y = sorted(data_dict.keys())
    pd.DataFrame({"symbol": symbols_with_2y}).to_csv(
        "symbols_with_2y_weekly_data.csv", index=False
    )

    print(f"\nSaved {len(symbols_with_2y)} symbols to symbols_with_2y_weekly_data.csv")


import os
import glob
import pandas as pd
import numpy as np
from tqdm import tqdm
from sklearn.linear_model import ElasticNetCV

# ────────────────────────────────────────────────────────────────────────────
# HELPERS: Indicator Functions (weekly‐frequency versions)
# ────────────────────────────────────────────────────────────────────────────

def compute_rsi(series, period=14):
    delta = series.diff()
    up = delta.clip(lower=0)
    down = -delta.clip(upper=0)
    avg_up = up.rolling(window=period, min_periods=period).mean()
    avg_down = down.rolling(window=period, min_periods=period).mean()
    rs = avg_up / avg_down
    return 100 - (100 / (1 + rs))

def compute_stoch(df, period=14, smooth_k=3):
    low_min  = df['low'].rolling(window=period, min_periods=period).min()
    high_max = df['high'].rolling(window=period, min_periods=period).max()
    stochK   = 100 * (df['close'] - low_min) / (high_max - low_min)
    stochD   = stochK.rolling(window=smooth_k, min_periods=smooth_k).mean()
    return stochK, stochD

def compute_stoch_rsi(series, period=14):
    rsi    = compute_rsi(series, period=period)
    rsimin = rsi.rolling(window=period, min_periods=period).min()
    rsimax = rsi.rolling(window=period, min_periods=period).max()
    return 100 * (rsi - rsimin) / (rsimax - rsimin)

def compute_cci(df, period=14):
    tp = (df['high'] + df['low'] + df['close']) / 3
    ma = tp.rolling(window=period, min_periods=period).mean()
    md = tp.rolling(window=period, min_periods=period).apply(
        lambda x: np.mean(np.abs(x - x.mean())), raw=True
    )
    return (tp - ma) / (0.015 * md)

def compute_ema(series, span):
    return series.ewm(span=span, adjust=False).mean()

def compute_macd(series, fast=12, slow=26, signal=9):
    ema_fast   = compute_ema(series, span=fast)
    ema_slow   = compute_ema(series, span=slow)
    macd_pct   = 100 * (ema_fast - ema_slow) / ema_fast
    macd_signal= compute_ema(macd_pct, span=signal)
    macd_diff  = macd_pct - macd_signal
    return macd_pct, macd_diff

def compute_sma(series, period):
    return series.rolling(window=period, min_periods=period).mean()

def compute_sma_dist(series, period):
    sma = compute_sma(series, period)
    return (sma / series) - 1

def compute_vol_macd(volume, fast=12, slow=26, signal=9):
    ema_fast_v = compute_ema(volume, span=fast)
    ema_slow_v = compute_ema(volume, span=slow)
    vol_macd_pct  = 100 * (ema_fast_v - ema_slow_v) / ema_fast_v
    vol_signal    = compute_ema(vol_macd_pct, span=signal)
    vol_macd_diff = vol_macd_pct - vol_signal
    return vol_macd_pct, vol_macd_diff

def compute_chaikin(df, period=20):
    mfm = ((df['close'] - df['low']) - (df['high'] - df['close'])) / (df['high'] - df['low'])
    mfv = mfm * df['volume']
    num = mfv.rolling(window=period, min_periods=period).sum()
    den = df['volume'].rolling(window=period, min_periods=period).sum()
    return num / den

def compute_bollinger(df, period=20, stddev=2):
    sma = df['close'].rolling(window=period, min_periods=period).mean()
    std = df['close'].rolling(window=period, min_periods=period).std()
    upper = sma + stddev * std
    lower = sma - stddev * std
    low_scaled   = lower   / df['close']
    mid_scaled   = sma     / df['close']
    up_scaled    = upper   / df['close']
    width_scaled = (upper - lower) / sma
    return low_scaled, mid_scaled, up_scaled, width_scaled

# ────────────────────────────────────────────────────────────────────────────
# 1) LOAD WEEKLY OHLCV & ALIGN DATES ACROSS SYMBOLS
# ────────────────────────────────────────────────────────────────────────────

data_dir = '.' 
pattern  = os.path.join(data_dir, '*_2y_weekly.csv')
all_csvs = glob.glob(pattern)

# Read each CSV into: ohlcv[symbol] = DataFrame(index=weekly_datetime)
ohlcv = {}
for fpath in all_csvs:
    sym = os.path.basename(fpath).replace('_2y_weekly.csv', '').replace('_', '/')
    df  = pd.read_csv(fpath, parse_dates=['datetime']).set_index('datetime').sort_index()
    ohlcv[sym] = df[['open','high','low','close','volume']]

# Find the common weekly dates (intersection of every symbol’s index)
date_sets = [set(df.index) for df in ohlcv.values()]
if not date_sets:
    raise RuntimeError("No CSV files found matching '*_2y_weekly.csv' in this directory.")
common_dates = sorted(date_sets[0].intersection(*date_sets[1:]))

# Trim every symbol’s DataFrame to these common_dates
for sym in list(ohlcv.keys()):
    ohlcv[sym] = ohlcv[sym].loc[common_dates]

# ────────────────────────────────────────────────────────────────────────────
# 2) COMPUTE WEEKLY RETURNS FOR EACH SYMBOL
# ────────────────────────────────────────────────────────────────────────────

# r_{t} = (close_t / close_{t-1}) - 1, shifted so r_{t} is at the index of t-1
returns = {}
for sym, df in ohlcv.items():
    returns[sym] = df['close'].pct_change().shift(-1)

returns_df = pd.DataFrame(returns).loc[common_dates[:-1]]

# ────────────────────────────────────────────────────────────────────────────
# 3) BUILD INDICATORS FOR EACH SYMBOL (DROP ANY LOOKBACK > 100 WEEKS)
# ────────────────────────────────────────────────────────────────────────────

indicators = {}
min_weeks   = 100  # maximum lookback now is 100 (SMA100, EMA26, etc.)

for sym, df in ohlcv.items():
    if len(df) < min_weeks:
        continue  # skip symbols with fewer than 100 weeks total

    ind = pd.DataFrame(index=df.index)

    # 3.1 Momentum Oscillators (14-week lookback)
    ind['RSI_14']      = compute_rsi(df['close'], 14)
    ind['StochK_14'], ind['StochD_14'] = compute_stoch(df, 14, 3)
    ind['StochRSI_14'] = compute_stoch_rsi(df['close'], 14)
    ind['CCI_14']      = compute_cci(df, 14)

    # 3.2 Price SMAs (omit SMA200)
    for p in [3, 5, 10, 20, 50, 100]:
        ind[f'SMA{p}_dist'] = compute_sma_dist(df['close'], p)

    ind['MACD_pct'], ind['MACD_diff'] = compute_macd(df['close'], 12, 26, 9)

    # 3.3 Volume SMAs (omit VolSMA200)
    for p in [3, 5, 10, 20, 50, 100]:
        sma_v = compute_sma(df['volume'], p)
        ind[f'VolSMA{p}_dist'] = (sma_v / df['volume']) - 1

    ind['VolMACD_pct'], ind['VolMACD_diff'] = compute_vol_macd(df['volume'], 12, 26, 9)
    ind['Chaikin_MF_20'] = compute_chaikin(df, 20)

    # 3.4 Bollinger Bands (20-week lookback)
    ind['Boll_Low'], ind['Boll_Mid'], ind['Boll_Up'], ind['Boll_Width'] = compute_bollinger(df, 20, 2)

    # 3.5 Drop the first 100 rows (to ensure SMA100, EMA26, etc. have valid data)
    ind = ind.iloc[min_weeks:].copy()
    # If after trimming everything is NaN, skip symbol
    if ind.isna().all(axis=None):
        continue

    indicators[sym] = ind.dropna(how='all')

if not indicators:
    raise RuntimeError("No symbols survived the ≥100-week indicator build. Check your CSVs and date range.")

# ────────────────────────────────────────────────────────────────────────────
# 4) ALIGN INDICATOR PANEL & RETURNS AGAINST COMMON DATES
# ────────────────────────────────────────────────────────────────────────────

ind_date_sets = [set(df.index) for df in indicators.values()]
common_ind_dates = sorted(ind_date_sets[0].intersection(*ind_date_sets[1:]))

# Trim each symbol’s indicators to common_ind_dates
for sym in list(indicators.keys()):
    indicators[sym] = indicators[sym].loc[common_ind_dates]

# Also trim returns_df to these common_ind_dates
returns_df = returns_df.loc[common_ind_dates]

symbols = list(indicators.keys())
dates   = common_ind_dates

# Build a “lagged, rank‐normalized” panel (z_panel) of indicators:
indicator_cols = list(indicators[symbols[0]].columns)
z_panel = {col: pd.DataFrame(index=dates, columns=symbols) for col in indicator_cols}

for col in indicator_cols:
    temp = pd.DataFrame(index=dates, columns=symbols)
    for sym in symbols:
        temp[sym] = indicators[sym][col]
    # Cross‐sectional rank normalization on each week
    ranks = temp.rank(axis=1, method='average', pct=True) - 0.5
    z_panel[col] = ranks.shift(1)   # lag by one week (z_{t-1} → index t)

# Trim returns_df one more time to ensure the same index
returns_df = returns_df.loc[z_panel[indicator_cols[0]].index]

# ────────────────────────────────────────────────────────────────────────────
# 5) BACKTEST SETUP: FIRST 52 WEEKS TRAIN → NEXT 52 WEEKS TEST
# ────────────────────────────────────────────────────────────────────────────

n_total  = len(dates)
n_train  = n_total // 2           # if two years = 104 weeks, n_train = 52
train_weeks = dates[:n_train]
test_weeks  = dates[n_train:-1]   # leave last for “next-week” return lookup

results = []

for t in tqdm(test_weeks, desc="Backtesting"):
    # 5.1 Build the training set: all weeks < t in train_weeks
    train_window = [d for d in train_weeks if dates.index(d) < dates.index(t)]
    if len(train_window) < 2:
        continue

    # 5.2 Fama–MacBeth: regress cross‐section at each u ∈ train_window[1:]
    alphas = {col: [] for col in indicator_cols}
    betas  = {col: [] for col in indicator_cols}
    weeks  = []

    for u in train_window[1:]:
        r_u = returns_df.loc[u]   # realized returns at week u
        weeks.append(u)
        for col in indicator_cols:
            z_u = z_panel[col].loc[u]
            valid = (~r_u.isna()) & (~z_u.isna())
            if valid.sum() < 2:
                alphas[col].append(np.nan)
                betas[col].append(np.nan)
                continue
            X = np.vstack([np.ones(valid.sum()), z_u[valid]]).T
            y = r_u[valid].values
            coef, *_ = np.linalg.lstsq(X, y, rcond=None)
            alphas[col].append(coef[0])
            betas[col].append(coef[1])

    if not weeks:
        continue

    alpha_df = pd.DataFrame(alphas, index=weeks)
    beta_df  = pd.DataFrame(betas,  index=weeks)

    # 5.3 Smooth α,β over up to last 52 weeks of training
    window_size = min(52, len(alpha_df))
    if window_size == 0:
        continue

    alpha_bar = alpha_df.iloc[-window_size:].mean()
    beta_bar  = beta_df.iloc[-window_size:].mean()

    # 5.4 One‐week‐ahead forecasts at date t
    fcasts = {col: alpha_bar[col] + beta_bar[col] * z_panel[col].loc[t]
              for col in indicator_cols}

    # 5.5 Build the pooled training DataFrame for ElasticNet
    pooled = []
    for u in weeks[-window_size:]:
        r_u = returns_df.loc[u]
        sub_alpha_bar = alpha_df.loc[:u].iloc[-window_size:].mean()
        sub_beta_bar  = beta_df.loc[:u].iloc[-window_size:].mean()
        for sym in symbols:
            feats = []
            valid_feats = True
            for col in indicator_cols:
                z_val = z_panel[col].loc[u, sym]
                if np.isnan(z_val):
                    valid_feats = False
                    break
                feats.append(sub_alpha_bar[col] + sub_beta_bar[col] * z_val)
            if not valid_feats:
                continue
            pooled.append([returns_df.at[u, sym]] + feats)

    if not pooled:
        continue

    enet_df = pd.DataFrame(pooled, columns=['return'] + indicator_cols)
    X = enet_df[indicator_cols].values
    y = enet_df['return'].values

    # 5.6 ElasticNetCV (50% L1, 50% L2) to pick positive indicators
    enet = ElasticNetCV(l1_ratio=0.5, cv=3).fit(X, y)
    selected = np.array(indicator_cols)[enet.coef_ > 0]
    if len(selected) == 0:
        continue

    # 5.7 CTREND score at t: average of selected forecasts
    fvals = np.vstack([fcasts[col] for col in selected])   # shape = (n_selected, n_coins)
    ctrend_scores = pd.Series(fvals.mean(axis=0), index=symbols)

    # 5.8 Build long-short: top 20% vs bottom 20%
    ranks = ctrend_scores.rank(method='first')
    N     = len(symbols)
    top_q  = ranks[ranks > 0.8 * N].index.tolist()
    bot_q  = ranks[ranks <= 0.2 * N].index.tolist()
    if not top_q or not bot_q:
        top_q = [ranks.idxmax()]
        bot_q = [ranks.idxmin()]

    # 5.9 Next‐week realized return
    next_idx = dates.index(t) + 1
    if next_idx >= len(dates):
        break
    next_d  = dates[next_idx]
    r_next  = returns_df.loc[next_d]

    # Equal‐weight long/short P&L
    ret_long  = r_next[top_q].mean()
    ret_short = r_next[bot_q].mean()
    strat_ret = ret_long - ret_short

    results.append({'date': t, 'strategy_ret': strat_ret})

# ────────────────────────────────────────────────────────────────────────────
# 6) COMPILE & OUTPUT BACKTEST RESULTS
# ────────────────────────────────────────────────────────────────────────────

backtest_df = pd.DataFrame(results).set_index('date')
backtest_df['cum_return'] = (1 + backtest_df['strategy_ret']).cumprod() - 1

print("\nBacktest performance (last 5 weeks):")
print(backtest_df.tail(5))

# Save to CSV if you want
backtest_df.to_csv("ctrend_backtest_results.csv")


# from collections import deque

# def fm_univariate(df, feature, window=FEATURE_WINDOW):
#     betas = deque(maxlen=window)
#     pred = pd.Series(index=df.index, dtype=float)
#     dates = sorted(df['ts'].unique())
#     for idx in range(window, len(dates)-1):
#         t = dates[idx]
#         hist_dates = dates[idx-window:idx]
#         hist = df[df['ts'].isin(hist_dates)]
#         X = sm.add_constant(hist[[feature]])
#         y = hist['ret_fwd']
#         res = sm.OLS(y, X).fit()
#         betas.append(res.params[feature])
#         beta_bar = np.mean(betas)
#         curr = df[df['ts']==t][[feature]]
#         pred.loc[curr.index] = beta_bar * curr[feature].values + res.params['const']
#     return pred.dropna()

# # compute univariate forecasts
# uni_forecasts = {}
# for feat in tqdm(feature_cols):
#     uni_forecasts[feat] = fm_univariate(weekly, feat)

# uni_df = pd.concat(uni_forecasts, axis=1).dropna()

# # Elastic Net selection on meta-features per date
# def enet_select(t):
#     rows = uni_df.loc[uni_df.index.get_level_values('ts')==t]
#     if rows.empty:
#         return pd.Series(dtype=float)
#     X = rows.values
#     y = weekly.loc[rows.index.get_level_values(0), 'ret_fwd']
#     if REG_STRENGTH==0:
#         # simple heuristic λ = σ * sqrt(2 log p / n)
#         REG = np.std(y) * math.sqrt(2*math.log(X.shape[1])/X.shape[0])
#     else:
#         REG = REG_STRENGTH
#     model = ElasticNet(alpha=REG, l1_ratio=ALPHA_MIX, fit_intercept=False, max_iter=5000)
#     model.fit(X, y)
#     keep_mask = model.coef_ > 0
#     kept = rows.iloc[:, keep_mask]
#     if kept.shape[1]==0:
#         kept = rows.iloc[:, :1]  # fallback one feature
#     ctrend = kept.mean(axis=1)
#     return ctrend

# uni_df.index.names = ['idx']  # ensure single index to fetch ts
# weekly = weekly.join(uni_df, how='left')

# ctrend_list = []
# for t in tqdm(sorted(weekly['ts'].unique())[::-1][:1]):  # run only for the latest ts
#     ctr = enet_select(t)
#     ctr.name = 'ctrend'
#     ctrend_list.append(ctr)

# ctrend = pd.concat(ctrend_list)
# weekly.loc[ctrend.index, 'ctrend'] = ctrend
# weekly.dropna(subset=['ctrend'], inplace=True)


latest_ts = weekly['ts'].max()
latest = weekly[weekly['ts'] == latest_ts].copy()
latest['rank'] = latest['ctrend'].rank(pct=True)
long_side  = latest[latest['rank'] >= LONG_Q]
short_side = latest[latest['rank'] <= SHORT_Q]

# equal weight within each side (or value‑weight by market cap if you have it)
long_side  = long_side.assign(weight =  1/len(long_side))
short_side = short_side.assign(weight = -1/len(short_side))
portfolio  = pd.concat([long_side, short_side])[['symbol','weight','ctrend','rank']]
portfolio.sort_values('weight', ascending=False)


portfolio.to_csv('ctrend_signals.csv', index=False)
print('Saved signals → ctrend_signals.csv')
