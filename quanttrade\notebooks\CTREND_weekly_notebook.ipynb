!pip install -q ccxt pandas pandas_ta statsmodels scikit-learn linearmodels tqdm binance-connector yfinance requests pycoingecko

import ccxt
import pandas as pd

def fetch_binance_usdt_active_coins(min_volume=1, top_n=None):
    """
    Fetch Binance USDT-quoted coins with 24h volume > min_volume,
    dedupe to one pair per base, and optionally take the top N.
    """
    # 1) Connect to Binance via CCXT
    exchange = ccxt.binance({'enableRateLimit': True})
    exchange.load_markets()

    # 2) Fetch all 24h ticker stats
    print("Fetching 24h tickers from Binance…")
    tickers = exchange.fetch_tickers()

    # 3) Build DataFrame of symbol vs. 24h quoteVolume
    df = pd.DataFrame.from_dict(tickers, orient='index')[['symbol', 'quoteVolume']].copy()
    df['quoteVolume'] = pd.to_numeric(df['quoteVolume'], errors='coerce').fillna(0.0)

    # 4) Keep only USDT-quoted pairs
    df_usdt = df[df['symbol'].str.endswith('/USDT')].copy()

    # 5) Filter out zero-volume pairs
    df_usdt = df_usdt[df_usdt['quoteVolume'] > min_volume]

    # 6) Extract base asset
    df_usdt['base'] = df_usdt['symbol'].str.split('/').str[0]

    # 7) Sort by descending volume
    df_usdt = df_usdt.sort_values('quoteVolume', ascending=False)

    # 8) Drop duplicate bases, keeping only the highest-volume pair
    df_unique = df_usdt.drop_duplicates(subset='base', keep='first')

    # 9) (Optional) Keep only the top N coins
    if top_n is not None:
        df_unique = df_unique.head(top_n)

    df_unique = df_unique.reset_index(drop=True)
    print(f"Found {len(df_unique)} USDT-quoted coins with volume > {min_volume}")

    # Show a sample
    print(df_unique[['base', 'symbol', 'quoteVolume']].head(20).to_string(index=False))
    return df_unique

if __name__ == "__main__":
    # e.g. only coins with > $1,000 24h volume, top 600
    df_active = fetch_binance_usdt_active_coins(min_volume=1_000, top_n=600)
    df_active.to_csv("binance_usdt_active.csv", index=False)


df

# def get_daily_data_yfinance(symbol, start_date, end_date=None):
#     """
#     Fallback function to get data from Yahoo Finance if Binance API fails.
    
#     Args:
#         symbol: Trading pair in format 'BTC/USDT'
#         start_date: Start date as string 'YYYY-MM-DD' or datetime object
#         end_date: Optional end date
        
#     Returns:
#         DataFrame with OHLCV data in the same format as Binance data
#     """
#     if not HAVE_YFINANCE:
#         print("yfinance not installed. Install with: pip install yfinance")
#         return pd.DataFrame()
        
#     # Convert Binance symbol format to Yahoo format (BTC/USDT -> BTC-USD)
#     base, quote = symbol.split('/')
#     # Yahoo typically uses USD instead of USDT for crypto
#     if quote == 'USDT':
#         quote = 'USD'
#     ticker = f"{base}-{quote}"
    
#     try:
#         # Fetch data
#         data = yf.download(ticker, start=start_date, end=end_date, interval='1d', progress=False)
        
#         if data.empty:
#             print(f"No data found for {ticker} on Yahoo Finance")
#             return pd.DataFrame()
            
#         # Convert to the same format as our Binance data
#         data = data.reset_index()
#         data.columns = ['date', 'open', 'high', 'low', 'close', 'adj_close', 'volume']
        
#         # Convert datetime to millisecond timestamp
#         data['ts'] = data['date'].apply(lambda x: int(x.timestamp() * 1000))
        
#         # Rename volume column to match Binance format
#         data = data.rename(columns={'volume': 'vol'})
        
#         # Select only the columns we need
#         return data[['ts', 'open', 'high', 'low', 'close', 'vol']]
        
#     except Exception as e:
#         print(f"Error fetching data from Yahoo Finance for {symbol}: {e}")
#         return pd.DataFrame()

# def get_daily_data():
#     """
#     Fetch daily OHLCV data for all symbols in UNIVERSE using the official Binance connector.
#     Implements caching to avoid unnecessary API calls.
#     """
#     # Initialize Binance client with rate limiting enabled
#     client_params = {'timeout': 30}
#     if BINANCE_BASE_URL:
#         client_params['base_url'] = BINANCE_BASE_URL
        
#     client = Spot(**client_params)
    
#     if DEBUG_MODE:
#         print("DEBUG: Initializing Binance Spot client")
#         print(f"DEBUG: Base URL: {client.base_url}")
#         print(f"DEBUG: Timeout setting: {client.timeout}")
    
#     all_dfs = []
    
#     # Use only BTC/USDT in test mode
#     symbols_to_process = ['BTC/USDT'] if TEST_MODE else UNIVERSE
#     if TEST_MODE and DEBUG_MODE:
#         print("DEBUG: Running in TEST MODE with only BTC/USDT")
        
#     for sym in tqdm(symbols_to_process):
#         try:
#             # Create cache path
#             cache = Path(DATA_DIR)/f"{sym.replace('/','_')}_{TIMEFRAME}.parquet"
            
#             # Determine start time for data fetching
#             if cache.exists():
#                 df = pd.read_parquet(cache)
#                 print(f"Loaded cached data for {sym}: {len(df)} records")
                
#                 # Get the latest timestamp and add one day to avoid duplicates
#                 latest_ts = pd.to_datetime(df['ts'].max(), unit='ms')
#                 start_time = int((latest_ts + timedelta(days=1)).timestamp() * 1000)
#             else:
#                 df = pd.DataFrame()
#                 # Calculate start time based on LOOKBACK_YEARS
#                 start_time = int((datetime.now() - timedelta(days=365*LOOKBACK_YEARS)).timestamp() * 1000)
            
#             # Only fetch new data if needed
#             if not cache.exists() or (datetime.now() - pd.to_datetime(df['ts'].max(), unit='ms')).days > 0:
#                 print(f"Fetching new data for {sym} since {datetime.fromtimestamp(start_time/1000)}")
                
#                 # Fetch klines (OHLCV) data
#                 klines = []
                
#                 # Handle pagination with retry logic
#                 retry_count = 0
#                 max_retries = 3
                
#                 while retry_count < max_retries:
#                     try:
#                         # Fetch up to 1000 candles at a time (Binance limit)
#                         # Convert symbol format for Binance API (BTC/USDT -> BTCUSDT)
#                         binance_symbol = sym.replace('/', '')
#                         print(f"Fetching data for symbol: {binance_symbol}")
                        
#                         temp_klines = client.klines(
#                             symbol=binance_symbol,
#                             interval=TIMEFRAME,
#                             startTime=start_time,
#                             limit=1000
#                         )
                        
#                         if not temp_klines:
#                             break
                            
#                         klines.extend(temp_klines)
                        
#                         # Update start_time for next batch
#                         start_time = temp_klines[-1][0] + 1
                        
#                         # If we got less than 1000 candles, we've reached the end
#                         if len(temp_klines) < 1000:
#                             break
                            
#                         # Be nice to the API
#                         time.sleep(0.5)
                        
#                     except Exception as e:
#                         retry_count += 1
#                         error_msg = str(e)
#                         print(f"Error fetching {sym}, attempt {retry_count}/{max_retries}: {error_msg}")
                        
#                         # Handle specific error types
#                         if "Invalid symbol" in error_msg:
#                             print(f"Symbol {binance_symbol} is not valid on Binance. Skipping further attempts.")
#                             break
#                         elif "timeout" in error_msg.lower():
#                             print("Timeout error. Waiting longer before retry...")
#                             time.sleep(5)  # Wait longer for timeout errors
#                         elif "rate limit" in error_msg.lower() or "too many requests" in error_msg.lower():
#                             wait_time = min(60, 5 * retry_count)  # Exponential backoff
#                             print(f"Rate limit hit. Waiting {wait_time} seconds before retry...")
#                             time.sleep(wait_time)
#                         else:
#                             # Generic error handling
#                             if retry_count >= max_retries:
#                                 print(f"Failed to fetch {sym} after {max_retries} attempts")
#                                 break
#                             time.sleep(2)  # Wait before retry
                
#                 # Process the new data if any was fetched
#                 if klines:
#                     # Convert to DataFrame
#                     new_df = pd.DataFrame(
#                         klines,
#                         columns=['ts', 'open', 'high', 'low', 'close', 'vol', 
#                                 'close_time', 'quote_vol', 'trades', 
#                                 'taker_base_vol', 'taker_quote_vol', 'ignore']
#                     )
                    
#                     # Keep only the columns we need
#                     new_df = new_df[['ts', 'open', 'high', 'low', 'close', 'vol']]
                    
#                     # Convert string values to numeric
#                     for col in ['open', 'high', 'low', 'close', 'vol']:
#                         new_df[col] = pd.to_numeric(new_df[col])
                    
#                     # Combine with existing data
#                     if not df.empty:
#                         df = pd.concat([df, new_df])
#                         df = df.drop_duplicates('ts').sort_values('ts')
#                     else:
#                         df = new_df
                    
#                     # Save to cache
#                     df.to_parquet(cache, index=False)
#                     print(f"Saved {len(df)} records for {sym}")
            
#             # Add symbol column and append to result
#             df['symbol'] = sym
#             all_dfs.append(df)
            
#         except Exception as e:
#             error_msg = str(e)
#             print(f"Error processing {sym} from Binance: {error_msg}")
            
#             # Try alternative Binance URLs if connection issue detected
#             if any(x in error_msg.lower() for x in ['timeout', 'connection', 'network']):
#                 alt_urls = [
#                     'https://api1.binance.com',
#                     'https://api2.binance.com',
#                     'https://api3.binance.com'
#                 ]
                
#                 for alt_url in alt_urls:
#                     if alt_url == client.base_url:
#                         continue  # Skip the URL we already tried
                        
#                     print(f"Trying alternative Binance URL: {alt_url}")
#                     try:
#                         alt_client = Spot(base_url=alt_url, timeout=30)
                        
#                         # Try a simple request to test the connection
#                         alt_client.time()
#                         print(f"Successfully connected to {alt_url}")
                        
#                         # Try to fetch data with this alternative URL
#                         binance_symbol = sym.replace('/', '')
#                         start_time = int((datetime.now() - timedelta(days=365*LOOKBACK_YEARS)).timestamp() * 1000)
                        
#                         klines = alt_client.klines(
#                             symbol=binance_symbol,
#                             interval=TIMEFRAME,
#                             startTime=start_time,
#                             limit=1000
#                         )
                        
#                         if klines:
#                             print(f"Successfully fetched data from alternative URL {alt_url}")
#                             # Process the data as before
#                             new_df = pd.DataFrame(
#                                 klines,
#                                 columns=['ts', 'open', 'high', 'low', 'close', 'vol', 
#                                         'close_time', 'quote_vol', 'trades', 
#                                         'taker_base_vol', 'taker_quote_vol', 'ignore']
#                             )
                            
#                             # Keep only the columns we need
#                             new_df = new_df[['ts', 'open', 'high', 'low', 'close', 'vol']]
                            
#                             # Convert string values to numeric
#                             for col in ['open', 'high', 'low', 'close', 'vol']:
#                                 new_df[col] = pd.to_numeric(new_df[col])
                                
#                             new_df['symbol'] = sym
#                             all_dfs.append(new_df)
                            
#                             # Break out of the alternative URL loop
#                             break
#                     except Exception as alt_e:
#                         print(f"Alternative URL {alt_url} also failed: {alt_e}")
#                         continue
            
#             # If all Binance URLs failed, try Yahoo Finance as a last resort
#             if HAVE_YFINANCE:
#                 print(f"Attempting to fetch {sym} from Yahoo Finance as fallback...")
#                 start_date = (datetime.now() - timedelta(days=365*LOOKBACK_YEARS)).strftime('%Y-%m-%d')
#                 yf_df = get_daily_data_yfinance(sym, start_date)
                
#                 if not yf_df.empty:
#                     print(f"Successfully fetched {len(yf_df)} records for {sym} from Yahoo Finance")
#                     yf_df['symbol'] = sym
#                     all_dfs.append(yf_df)
    
#     # Combine all dataframes
#     if not all_dfs:
#         raise ValueError("Failed to fetch data for any symbols from both Binance and Yahoo Finance")
    
#     result = pd.concat(all_dfs).reset_index(drop=True)
#     print(f"Total records: {len(result)}")
#     return result

# daily = get_daily_data()
# print('Daily bars:', daily.shape)


# def to_weekly(df):
#     out = df.set_index('ts')
#     out.index = pd.to_datetime(out.index, unit='ms', utc=True)
#     out = (out.groupby('symbol')
#              .resample('W-FRI')
#              .agg({'open':'first','high':'max','low':'min','close':'last','vol':'sum'})
#              .dropna())
#     out = out.reset_index()
#     return out

# weekly = to_weekly(daily)

# # compute indicators within each symbol
# def add_ta(group):
#     g = group.sort_values('ts').copy()
#     g['rsi14'] = ta.rsi(g['close'], 14)
#     g['sma10'] = ta.sma(g['close'], 10)
#     g['ema20'] = ta.ema(g['close'], 20)
#     macd = ta.macd(g['close'])  # returns MACD, signal, hist
#     g[['macd','macds','macdh']] = macd
#     # ... add remaining indicators as needed ...
#     return g

# weekly = weekly.groupby('symbol', group_keys=False).apply(add_ta)

# # lag features & rank-scale cross-sectionally
# feature_cols = [c for c in weekly.columns if c not in ['ts','symbol','open','high','low','close','vol']]
# weekly[feature_cols] = weekly.groupby('ts')[feature_cols] \
#     .apply(lambda df_: (df_.rank(pct=True) - 0.5))

# # forward return target
# weekly['ret_fwd'] = weekly.groupby('symbol')['close'].pct_change().shift(-1)
# weekly = weekly.dropna(subset=feature_cols + ['ret_fwd'])
# print('Weekly rows:', weekly.shape)


# from collections import deque

# def fm_univariate(df, feature, window=FEATURE_WINDOW):
#     betas = deque(maxlen=window)
#     pred = pd.Series(index=df.index, dtype=float)
#     dates = sorted(df['ts'].unique())
#     for idx in range(window, len(dates)-1):
#         t = dates[idx]
#         hist_dates = dates[idx-window:idx]
#         hist = df[df['ts'].isin(hist_dates)]
#         X = sm.add_constant(hist[[feature]])
#         y = hist['ret_fwd']
#         res = sm.OLS(y, X).fit()
#         betas.append(res.params[feature])
#         beta_bar = np.mean(betas)
#         curr = df[df['ts']==t][[feature]]
#         pred.loc[curr.index] = beta_bar * curr[feature].values + res.params['const']
#     return pred.dropna()

# # compute univariate forecasts
# uni_forecasts = {}
# for feat in tqdm(feature_cols):
#     uni_forecasts[feat] = fm_univariate(weekly, feat)

# uni_df = pd.concat(uni_forecasts, axis=1).dropna()

# # Elastic Net selection on meta-features per date
# def enet_select(t):
#     rows = uni_df.loc[uni_df.index.get_level_values('ts')==t]
#     if rows.empty:
#         return pd.Series(dtype=float)
#     X = rows.values
#     y = weekly.loc[rows.index.get_level_values(0), 'ret_fwd']
#     if REG_STRENGTH==0:
#         # simple heuristic λ = σ * sqrt(2 log p / n)
#         REG = np.std(y) * math.sqrt(2*math.log(X.shape[1])/X.shape[0])
#     else:
#         REG = REG_STRENGTH
#     model = ElasticNet(alpha=REG, l1_ratio=ALPHA_MIX, fit_intercept=False, max_iter=5000)
#     model.fit(X, y)
#     keep_mask = model.coef_ > 0
#     kept = rows.iloc[:, keep_mask]
#     if kept.shape[1]==0:
#         kept = rows.iloc[:, :1]  # fallback one feature
#     ctrend = kept.mean(axis=1)
#     return ctrend

# uni_df.index.names = ['idx']  # ensure single index to fetch ts
# weekly = weekly.join(uni_df, how='left')

# ctrend_list = []
# for t in tqdm(sorted(weekly['ts'].unique())[::-1][:1]):  # run only for the latest ts
#     ctr = enet_select(t)
#     ctr.name = 'ctrend'
#     ctrend_list.append(ctr)

# ctrend = pd.concat(ctrend_list)
# weekly.loc[ctrend.index, 'ctrend'] = ctrend
# weekly.dropna(subset=['ctrend'], inplace=True)


latest_ts = weekly['ts'].max()
latest = weekly[weekly['ts'] == latest_ts].copy()
latest['rank'] = latest['ctrend'].rank(pct=True)
long_side  = latest[latest['rank'] >= LONG_Q]
short_side = latest[latest['rank'] <= SHORT_Q]

# equal weight within each side (or value‑weight by market cap if you have it)
long_side  = long_side.assign(weight =  1/len(long_side))
short_side = short_side.assign(weight = -1/len(short_side))
portfolio  = pd.concat([long_side, short_side])[['symbol','weight','ctrend','rank']]
portfolio.sort_values('weight', ascending=False)


portfolio.to_csv('ctrend_signals.csv', index=False)
print('Saved signals → ctrend_signals.csv')
